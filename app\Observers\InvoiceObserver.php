<?php

namespace App\Observers;

use App\Models\Invoice;
use Illuminate\Support\Facades\Log;

class InvoiceObserver
{
    /**
     * Handle the Invoice "updating" event.
     */
    public function updating(Invoice $invoice): void
    {
        // Track status changes
        if ($invoice->isDirty('status')) {
            $oldStatus = $invoice->getOriginal('status');
            $newStatus = $invoice->status;
            
            Log::info('Invoice status changing', [
                'invoice_id' => $invoice->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'old_status_label' => Invoice::STATUS_ARR[$oldStatus] ?? 'Unknown',
                'new_status_label' => Invoice::STATUS_ARR[$newStatus] ?? 'Unknown'
            ]);
        }
    }

    /**
     * Handle the Invoice "updated" event.
     */
    public function updated(Invoice $invoice): void
    {
        // Log successful status updates
        if ($invoice->wasChanged('status')) {
            Log::info('Invoice status updated successfully', [
                'invoice_id' => $invoice->id,
                'new_status' => $invoice->status,
                'status_label' => $invoice->status_label
            ]);
        }
    }

    /**
     * Handle the Invoice "created" event.
     */
    public function created(Invoice $invoice): void
    {
        Log::info('Invoice created', [
            'invoice_id' => $invoice->id,
            'status' => $invoice->status,
            'status_label' => $invoice->status_label
        ]);
    }
}
