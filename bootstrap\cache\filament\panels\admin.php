<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.resources.admin-resource.pages.create-admin' => 'App\\Filament\\Resources\\AdminResource\\Pages\\CreateAdmin',
    'app.filament.resources.admin-resource.pages.edit-admin' => 'App\\Filament\\Resources\\AdminResource\\Pages\\EditAdmin',
    'app.filament.resources.admin-resource.pages.list-admins' => 'App\\Filament\\Resources\\AdminResource\\Pages\\ListAdmins',
    'app.filament.resources.admin-resource.pages.view-admin' => 'App\\Filament\\Resources\\AdminResource\\Pages\\ViewAdmin',
    'app.filament.resources.categories-resource.pages.manage-categories' => 'App\\Filament\\Resources\\CategoriesResource\\Pages\\ManageCategories',
    'app.filament.resources.client-resource.pages.create-client' => 'App\\Filament\\Resources\\ClientResource\\Pages\\CreateClient',
    'app.filament.resources.client-resource.pages.edit-client' => 'App\\Filament\\Resources\\ClientResource\\Pages\\EditClient',
    'app.filament.resources.client-resource.pages.list-clients' => 'App\\Filament\\Resources\\ClientResource\\Pages\\ListClients',
    'app.filament.resources.client-resource.pages.view-client' => 'App\\Filament\\Resources\\ClientResource\\Pages\\ViewClient',
    'app.filament.resources.payment-qr-code-resource.pages.manage-payment-qr-codes' => 'App\\Filament\\Resources\\PaymentQrCodeResource\\Pages\\ManagePaymentQrCodes',
    'app.filament.resources.payment-resource.pages.list-payments' => 'App\\Filament\\Resources\\PaymentResource\\Pages\\ListPayments',
    'app.filament.resources.products-resource.pages.create-products' => 'App\\Filament\\Resources\\ProductsResource\\Pages\\CreateProducts',
    'app.filament.resources.products-resource.pages.edit-products' => 'App\\Filament\\Resources\\ProductsResource\\Pages\\EditProducts',
    'app.filament.resources.products-resource.pages.list-products' => 'App\\Filament\\Resources\\ProductsResource\\Pages\\ListProducts',
    'app.filament.resources.products-resource.pages.view-products' => 'App\\Filament\\Resources\\ProductsResource\\Pages\\ViewProducts',
    'app.filament.resources.service-type-resource.pages.create-service-type' => 'App\\Filament\\Resources\\ServiceTypeResource\\Pages\\CreateServiceType',
    'app.filament.resources.service-type-resource.pages.edit-service-type' => 'App\\Filament\\Resources\\ServiceTypeResource\\Pages\\EditServiceType',
    'app.filament.resources.service-type-resource.pages.list-service-types' => 'App\\Filament\\Resources\\ServiceTypeResource\\Pages\\ListServiceTypes',
    'app.filament.resources.service-type-resource.pages.view-service-type' => 'App\\Filament\\Resources\\ServiceTypeResource\\Pages\\ViewServiceType',
    'app.filament.resources.taxes-resource.pages.manage-taxes' => 'App\\Filament\\Resources\\TaxesResource\\Pages\\ManageTaxes',
    'app.filament.client.resources.invoice-resource.pages.create-invoice' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\CreateInvoice',
    'app.filament.client.resources.invoice-resource.pages.edit-invoice' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\EditInvoice',
    'app.filament.client.resources.invoice-resource.pages.invoice-payment-page' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\InvoicePaymentPage',
    'app.filament.client.resources.invoice-resource.pages.list-invoices' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\ListInvoices',
    'app.filament.client.resources.invoice-resource.pages.view-invoice' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\ViewInvoice',
    'app.filament.client.resources.quote-resource.pages.create-quote' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\CreateQuote',
    'app.filament.client.resources.quote-resource.pages.edit-quote' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\EditQuote',
    'app.filament.client.resources.quote-resource.pages.list-quotes' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\ListQuotes',
    'app.filament.client.resources.quote-resource.pages.view-quote' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\ViewQuote',
    'app.filament.client.resources.transaction-resource.pages.list-transactions' => 'App\\Filament\\Client\\Resources\\TransactionResource\\Pages\\ListTransactions',
    'app.filament.pages.auth.edit-profile' => 'App\\Filament\\Pages\\Auth\\EditProfile',
    'app.filament.pages.auth.request-password-reset' => 'App\\Filament\\Pages\\Auth\\RequestPasswordReset',
    'app.filament.pages.dashboard' => 'App\\Filament\\Pages\\Dashboard',
    'app.filament.pages.invoice-templates' => 'App\\Filament\\Pages\\InvoiceTemplates',
    'app.filament.client.pages.currency-report' => 'App\\Filament\\Client\\Pages\\CurrencyReport',
    'app.filament.client.pages.dashboard' => 'App\\Filament\\Client\\Pages\\Dashboard',
    'app.filament.clusters.countries' => 'App\\Filament\\Clusters\\Countries',
    'app.filament.clusters.countries.resources.cities-resource.pages.manage-cities' => 'App\\Filament\\Clusters\\Countries\\Resources\\CitiesResource\\Pages\\ManageCities',
    'app.filament.clusters.countries.resources.countries-resource.pages.manage-countries' => 'App\\Filament\\Clusters\\Countries\\Resources\\CountriesResource\\Pages\\ManageCountries',
    'app.filament.clusters.countries.resources.states-resource.pages.manage-states' => 'App\\Filament\\Clusters\\Countries\\Resources\\StatesResource\\Pages\\ManageStates',
    'app.filament.clusters.settings' => 'App\\Filament\\Clusters\\Settings',
    'app.filament.clusters.settings.pages.general' => 'App\\Filament\\Clusters\\Settings\\Pages\\General',
    'app.filament.clusters.settings.pages.invoice-settings' => 'App\\Filament\\Clusters\\Settings\\Pages\\InvoiceSettings',
    'app.filament.clusters.settings.pages.payment-gateway' => 'App\\Filament\\Clusters\\Settings\\Pages\\PaymentGateway',
    'app.filament.clusters.settings.resources.currency-resource.pages.manage-currencies' => 'App\\Filament\\Clusters\\Settings\\Resources\\CurrencyResource\\Pages\\ManageCurrencies',
    'app.filament.widgets.dashboard-overview' => 'App\\Filament\\Widgets\\DashboardOverview',
    'app.filament.widgets.income-overview' => 'App\\Filament\\Widgets\\IncomeOverview',
    'app.filament.widgets.invoice-overview' => 'App\\Filament\\Widgets\\InvoiceOverview',
    'app.filament.widgets.payment-overview' => 'App\\Filament\\Widgets\\PaymentOverview',
    'app.filament.client.widgets.dashbaord-overview' => 'App\\Filament\\Client\\Widgets\\DashbaordOverview',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
    'filament.pages.auth.password-reset.reset-password' => 'Filament\\Pages\\Auth\\PasswordReset\\ResetPassword',
  ),
  'clusters' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Countries.php' => 'App\\Filament\\Clusters\\Countries',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Settings.php' => 'App\\Filament\\Clusters\\Settings',
  ),
  'clusteredComponents' => 
  array (
    'App\\Filament\\Clusters\\Settings' => 
    array (
      0 => 'App\\Filament\\Clusters\\Settings\\Pages\\General',
      1 => 'App\\Filament\\Clusters\\Settings\\Pages\\InvoiceSettings',
      2 => 'App\\Filament\\Clusters\\Settings\\Pages\\PaymentGateway',
      3 => 'App\\Filament\\Clusters\\Settings\\Resources\\CurrencyResource',
    ),
    'App\\Filament\\Clusters\\Countries' => 
    array (
      0 => 'App\\Filament\\Clusters\\Countries\\Resources\\CitiesResource',
      1 => 'App\\Filament\\Clusters\\Countries\\Resources\\CountriesResource',
      2 => 'App\\Filament\\Clusters\\Countries\\Resources\\StatesResource',
    ),
  ),
  'clusterDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Clusters',
  ),
  'clusterNamespaces' => 
  array (
    0 => 'App\\Filament\\Clusters',
  ),
  'pages' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Pages\\Dashboard.php' => 'App\\Filament\\Pages\\Dashboard',
    'C:\\DCF\\invoices\\app\\Filament\\Pages\\InvoiceTemplates.php' => 'App\\Filament\\Pages\\InvoiceTemplates',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Pages\\CurrencyReport.php' => 'App\\Filament\\Client\\Pages\\CurrencyReport',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Pages\\Dashboard.php' => 'App\\Filament\\Client\\Pages\\Dashboard',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Countries.php' => 'App\\Filament\\Clusters\\Countries',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Settings.php' => 'App\\Filament\\Clusters\\Settings',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Settings\\Pages\\General.php' => 'App\\Filament\\Clusters\\Settings\\Pages\\General',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Settings\\Pages\\InvoiceSettings.php' => 'App\\Filament\\Clusters\\Settings\\Pages\\InvoiceSettings',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Settings\\Pages\\PaymentGateway.php' => 'App\\Filament\\Clusters\\Settings\\Pages\\PaymentGateway',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Pages',
    1 => 'C:\\DCF\\invoices\\app\\Filament/Client/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
    1 => 'App\\Filament\\Client\\Pages',
  ),
  'resources' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\AdminResource.php' => 'App\\Filament\\Resources\\AdminResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\CategoriesResource.php' => 'App\\Filament\\Resources\\CategoriesResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\ClientResource.php' => 'App\\Filament\\Resources\\ClientResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\PaymentQrCodeResource.php' => 'App\\Filament\\Resources\\PaymentQrCodeResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\PaymentResource.php' => 'App\\Filament\\Resources\\PaymentResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\ProductsResource.php' => 'App\\Filament\\Resources\\ProductsResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\ServiceTypeResource.php' => 'App\\Filament\\Resources\\ServiceTypeResource',
    'C:\\DCF\\invoices\\app\\Filament\\Resources\\TaxesResource.php' => 'App\\Filament\\Resources\\TaxesResource',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Resources\\InvoiceResource.php' => 'App\\Filament\\Client\\Resources\\InvoiceResource',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Resources\\QuoteResource.php' => 'App\\Filament\\Client\\Resources\\QuoteResource',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Resources\\TransactionResource.php' => 'App\\Filament\\Client\\Resources\\TransactionResource',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Countries\\Resources\\CitiesResource.php' => 'App\\Filament\\Clusters\\Countries\\Resources\\CitiesResource',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Countries\\Resources\\CountriesResource.php' => 'App\\Filament\\Clusters\\Countries\\Resources\\CountriesResource',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Countries\\Resources\\StatesResource.php' => 'App\\Filament\\Clusters\\Countries\\Resources\\StatesResource',
    'C:\\DCF\\invoices\\app\\Filament\\Clusters\\Settings\\Resources\\CurrencyResource.php' => 'App\\Filament\\Clusters\\Settings\\Resources\\CurrencyResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Resources',
    1 => 'C:\\DCF\\invoices\\app\\Filament/Client/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
    1 => 'App\\Filament\\Client\\Resources',
  ),
  'widgets' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Widgets\\DashboardOverview.php' => 'App\\Filament\\Widgets\\DashboardOverview',
    'C:\\DCF\\invoices\\app\\Filament\\Widgets\\IncomeOverview.php' => 'App\\Filament\\Widgets\\IncomeOverview',
    'C:\\DCF\\invoices\\app\\Filament\\Widgets\\InvoiceOverview.php' => 'App\\Filament\\Widgets\\InvoiceOverview',
    'C:\\DCF\\invoices\\app\\Filament\\Widgets\\PaymentOverview.php' => 'App\\Filament\\Widgets\\PaymentOverview',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Widgets\\DashbaordOverview.php' => 'App\\Filament\\Client\\Widgets\\DashbaordOverview',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Widgets',
    1 => 'C:\\DCF\\invoices\\app\\Filament/Client/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
    1 => 'App\\Filament\\Client\\Widgets',
  ),
);