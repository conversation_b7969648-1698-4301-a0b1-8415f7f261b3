<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.client.resources.invoice-resource.pages.create-invoice' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\CreateInvoice',
    'app.filament.client.resources.invoice-resource.pages.edit-invoice' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\EditInvoice',
    'app.filament.client.resources.invoice-resource.pages.invoice-payment-page' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\InvoicePaymentPage',
    'app.filament.client.resources.invoice-resource.pages.list-invoices' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\ListInvoices',
    'app.filament.client.resources.invoice-resource.pages.view-invoice' => 'App\\Filament\\Client\\Resources\\InvoiceResource\\Pages\\ViewInvoice',
    'app.filament.client.resources.quote-resource.pages.create-quote' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\CreateQuote',
    'app.filament.client.resources.quote-resource.pages.edit-quote' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\EditQuote',
    'app.filament.client.resources.quote-resource.pages.list-quotes' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\ListQuotes',
    'app.filament.client.resources.quote-resource.pages.view-quote' => 'App\\Filament\\Client\\Resources\\QuoteResource\\Pages\\ViewQuote',
    'app.filament.client.resources.transaction-resource.pages.list-transactions' => 'App\\Filament\\Client\\Resources\\TransactionResource\\Pages\\ListTransactions',
    'app.filament.client.pages.currency-report' => 'App\\Filament\\Client\\Pages\\CurrencyReport',
    'app.filament.client.pages.dashboard' => 'App\\Filament\\Client\\Pages\\Dashboard',
    'app.filament.client.widgets.dashbaord-overview' => 'App\\Filament\\Client\\Widgets\\DashbaordOverview',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'app.filament.pages.auth.request-password-reset' => 'App\\Filament\\Pages\\Auth\\RequestPasswordReset',
    'filament.pages.auth.password-reset.reset-password' => 'Filament\\Pages\\Auth\\PasswordReset\\ResetPassword',
    'app.filament.pages.auth.edit-profile' => 'App\\Filament\\Pages\\Auth\\EditProfile',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Pages\\CurrencyReport.php' => 'App\\Filament\\Client\\Pages\\CurrencyReport',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Pages\\Dashboard.php' => 'App\\Filament\\Client\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Client/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Client\\Pages',
  ),
  'resources' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Resources\\InvoiceResource.php' => 'App\\Filament\\Client\\Resources\\InvoiceResource',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Resources\\QuoteResource.php' => 'App\\Filament\\Client\\Resources\\QuoteResource',
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Resources\\TransactionResource.php' => 'App\\Filament\\Client\\Resources\\TransactionResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Client/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Client\\Resources',
  ),
  'widgets' => 
  array (
    'C:\\DCF\\invoices\\app\\Filament\\Client\\Widgets\\DashbaordOverview.php' => 'App\\Filament\\Client\\Widgets\\DashbaordOverview',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\DCF\\invoices\\app\\Filament/Client/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Client\\Widgets',
  ),
);